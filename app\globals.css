body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background-color: #0b1a24;
  color: #d8d8d8;
}
header {
  text-align: center;
  padding: 2rem 1rem 1rem;
  display: flex;
  justify-content: center;
}

.logo-container {
  width: 100%;
  max-width: 500px;
  display: flex;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.logo {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0px 5px 8px rgba(0, 0, 0, 0.7)) drop-shadow(0px 10px 15px rgba(0, 0, 0, 0.4));
  transition: transform 0.3s ease, filter 0.3s ease;
}

.logo:hover {
  transform: scale(1.03);
  filter: drop-shadow(0px 8px 12px rgba(0, 0, 0, 0.8)) drop-shadow(0px 15px 25px rgba(0, 0, 0, 0.5));
}
nav {
  position: fixed;
  top: 15%;
  left: 0;
  transform: translateY(-15%);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.2rem;
}
nav a {
  color: #ffffff;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease, transform 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

nav a:hover {
  color: #1e90ff;
  transform: translateX(5px);
}
.hero {
  text-align: center;
  padding: 3rem 1rem 1rem;
}
.hero h1 {
  font-size: 2.5rem;
  color: #d8d8d8;
  text-transform: uppercase;
}
.hero p {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 1rem auto;
  color: #b0b0b0;
}
.hero .cta-button {
  margin-top: 2rem;
}
.hero .cta-button button {
  background-color: #1e90ff;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
}

.cta-link {
  text-decoration: none;
  display: inline-block;
}
section {
  padding: 3rem 1.5rem;
  max-width: 900px;
  margin: auto;
}
h2 {
  color: #d8d8d8;
  font-size: 1.8rem;
  text-transform: uppercase;
}
.services {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}
.service {
  background-color: #132531;
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid #1f3544;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.service-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}
.service h3 {
  margin-top: 0;
}
.cta-bottom {
  text-align: center;
  padding: 3rem 1.5rem;
  background-color: #132531;
}
.cta-bottom button {
  background-color: #1e90ff;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
}
footer {
  text-align: center;
  padding: 2rem 1rem;
  font-size: 0.9rem;
  color: #999;
}
@media(max-width: 768px) {
  .services {
    grid-template-columns: 1fr;
  }
  nav {
    display: none;
  }
}
