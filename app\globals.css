/* Enhanced IMPAXX Design - All HTML features preserved */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #0a0a0a;
    min-height: 100vh;
    overflow-x: hidden;
    color: #fff;
    line-height: 1.6;
}

/* Main background with better browser support */
.main-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    opacity: 0.95;
}

/* Animated overlay */
.bg-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.2) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(1deg); }
    66% { transform: translateY(-20px) rotate(-1deg); }
}

/* Optimized particles */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    animation: particle-float 8s ease-in-out infinite;
}

@keyframes particle-float {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.4; }
    50% { transform: translateY(-15px) scale(1.2); opacity: 0.8; }
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        rgba(139, 92, 246, 0.1) 0%,
        rgba(6, 182, 212, 0.1) 50%,
        rgba(139, 92, 246, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav.scrolled {
    background: rgba(0, 0, 0, 0.95);
    padding: 0.5rem 0;
}

.nav.scrolled::before {
    opacity: 1;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

/* Logo Enhancement */
.logo {
    font-size: 2.2rem;
    font-weight: 900;
    background: linear-gradient(135deg, #fff, #8b5cf6, #06b6d4);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: #fff; /* Fallback */
    text-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
    transition: all 0.3s ease;
    position: relative;
}

.logo::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #8b5cf6, #06b6d4);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
}

.logo:hover::after {
    transform: scaleX(1);
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    position: relative;
}
